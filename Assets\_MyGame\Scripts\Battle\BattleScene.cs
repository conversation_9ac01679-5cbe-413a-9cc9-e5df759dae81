using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using FairyGUI;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.Rendering.Universal;

public class BattleScene : MonoBehaviour
{
    private static readonly string RES_MAJIANG_GROUP = "MaJiangGroup";
    private static readonly string RES_MAJIANG_Item = "MaJiangItem";
    public static BattleScene Inst { get; private set; }
    public Camera mainCamera;
    public LayerMask clickLayerMask;
    public CubeArea itemArea;
    public CubeArea bornTopArea;
    public CubeArea bornArea;
    public SlotBar slotBar;
    public GameObject coverCube;
    public GameObject shuffleCube;
    public Transform[] guideTrans;
    public int itemMajiangMaxCount = 2;
    public Vector2 createItemMajiangInterval = new Vector2(3 * 60 - 15f, 3 * 60 + 15f);
    private float _leftCreateItemMajiangTime = 60;
    private int _itemMajiangCount = 0;
    [NonSerialized] public Vector3 bornAreaPos;
    // public Dictionary<int, MaJiang> majiangDic = new Dictionary<int, MaJiang>();
    private int totalCount;
    private BattleHandler battleHandler;
    private Vector3 initCameraPos;
    private bool isShuffling = false;
    private void Awake()
    {
        Inst = this;
        if (Camera.main != null)
        {
            mainCamera.GetUniversalAdditionalCameraData().renderType = CameraRenderType.Overlay;
            var addCameraData = Camera.main.GetUniversalAdditionalCameraData();
            if (!addCameraData.cameraStack.Contains(mainCamera))
            {
                addCameraData.cameraStack.Insert(0, mainCamera);
            }
        }
        initCameraPos = mainCamera.transform.position;
        bornAreaPos = bornArea.transform.position;
        shuffleCube.SetActive(false);
    }

    public void StartGuide(int typeCount, MaJiangVo[] majiangVos)
    {
        if (majiangVos.Length > 6)
        {
            Debug.LogError("nums.Length > 6");
            return;
        }
        OnGameStart();
        MajiangManager.Inst.Clear();
        totalCount = majiangVos.Length;
        List<MaJiangGroup> majiangs = new List<MaJiangGroup>();
        for (int i = 0; i < majiangVos.Length; i++)
        {
            var tran = guideTrans[i];
            var majiangVo = majiangVos[i];
            var maJiang = PoolMgr.Inst.Get<MaJiangGroup>("MaJiangGroup");
            maJiang.transform.SetPositionAndRotation(tran.position, tran.rotation);
            maJiang.SetData(majiangVo);

            majiangs.Add(maJiang);
            MajiangManager.Inst.AddMajiang(maJiang);
        }
        slotBar.Init(0);
        NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
        UpdateCamera(majiangs.Count);
        GuideManager.Instance.EnqueueStep(GuideSetting.GetNewPlayerGuide(majiangs), (step) =>
        {
            GGISdk.Inst.ReportGuideComplete(step);
        });
    }

    private void OnGameStart()
    {
        GameGlobal.ResetLevelStar();
        GameGlobal.ResetComboCount();
    }

    private void EnableCoverCube()
    {
        coverCube.SetActive(true);
        DOVirtual.DelayedCall(2f, () =>
        {
            if (this == null)
                return;
            coverCube.SetActive(false);
        });
    }

    internal void StartGame(BattleHandler battleHandler, List<MaJiangVo> nums, int lockSlotCount)
    {
        this.battleHandler = battleHandler;
        this.totalCount = nums.Count;
        _leftCreateItemMajiangTime = UnityEngine.Random.Range(createItemMajiangInterval.x, createItemMajiangInterval.y);
        var majiangDic = MajiangManager.Inst.GetMajiangDic();
        foreach (var item in majiangDic)
        {
            item.Value.Release();
        }
        slotBar.Init(lockSlotCount);

        MajiangManager.Inst.Clear();
        OnGameStart();
        EnableCoverCube();
        StartCoroutine(CreateMajiangsInFrames(nums));
        UpdateCamera(nums.Count);
#if UNITY_EDITOR
        // var test = new int[] { 3, 5, 5, 4, 4 };//4,3,3 0.1  
        // var test = new int[] { 1, 1, 2, 3, 4, 5 };//1,5  0.6
        // var test = new int[] { 5, 5, 2, 1, 6 };//5,1,2  0.6
        // for (int i = 0; i < test.Length; i++)
        // {
        //     var maJiang = PoolMgr.Inst.Get<MaJiang>("MaJiang");
        //     maJiang.Num = test[i];
        //     maJiang.name = "MaJiang" + maJiang.Num;
        //     slotBar.Add(maJiang);
        // }
#endif
    }

    IEnumerator CreateMajiangsInFrames(List<MaJiangVo> nums)
    {
        var majiangCount = nums.Count;
        var totalLayer = 8;
        for (int i = 0; i < majiangCount; i++)
        {
            Vector3 bornPos = bornArea.GetPosBottom2Top(0, totalLayer);
            CreateMajiang(bornPos, nums[i]);
            if (i % 10 == 0)
            {
                yield return new WaitForSeconds(0.05f);
            }
        }
        OnCreateMajiangComplete();
        isShuffling = false;
    }

    private void OnCreateMajiangComplete()
    {
        Timer.Add(0.5f, 1, () =>
        {
            if (this == null) return;
            NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
            EffectPool.Play("EffectFlip", 3);

            var majiangDic = MajiangManager.Inst.GetMajiangDic();
            // var totalCount = majiangDic.Count;
            // var curCount = 0;
            // foreach (var item in majiangDic)
            // {
            //     curCount++;
            //     _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
            //     // if (Quaternion.Angle(item.Value.transform.rotation, Quaternion.identity) > 80)
            //     // {
            //     //     _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
            //     // }
            // }
        });
    }

    private void CreateMajiang(Vector3 pos, MaJiangVo majiangVo)
    {
        var maJiang = PoolMgr.Inst.Get<MaJiangGroup>("MaJiangGroup");
        maJiang.transform.position = pos;
        maJiang.transform.rotation = Quaternion.identity;
        maJiang.SetData(majiangVo);
        maJiang.name = majiangVo.type.ToString() + majiangVo.num1 + "," + majiangVo.num2;
        MajiangManager.Inst.AddMajiang(maJiang);
    }

    private void Update()
    {
        // _leftCreateItemMajiangTime -= Time.deltaTime;
        // if (_leftCreateItemMajiangTime < 0)
        // {
        //     _leftCreateItemMajiangTime = UnityEngine.Random.Range(createItemMajiangInterval.x, createItemMajiangInterval.y); ;
        //     if (GameGlobal.Level > 1 && _itemMajiangCount < itemMajiangMaxCount)
        //     {
        //         var itemNum = UnityEngine.Random.Range(1, Mathf.Min(GameGlobal.Level, 5));
        //         var pos = itemArea.GetRandonPos();
        //         CreateMajiang(pos, new MaJiangVo(MaJiangType.Item, itemNum));
        //     }
        //     _itemMajiangCount++;
        // }

        if (Input.GetMouseButtonDown(0))
        {
            if (IsTouchUI || isShuffling)
                return;
            var ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, 100, clickLayerMask))
            {
                var maJiang = hit.collider.gameObject.GetComponentInParent<MaJiangGroup>();
                if (maJiang != null)
                {
                    Platform.Instance.VibrateShort();
                    if (slotBar.IsSlotFull)
                    {
                        return;
                    }
                    SoundManager.PlayEffect("moveEnd");
                    maJiang.ClickTime = Time.realtimeSinceStartup;
                    maJiang.OnClick?.Invoke();
                    if (maJiang.IsItem)
                    {
                        NotifyMgr.Event(NotifyNames.SelectItemMajiang, maJiang.Id);
                    }
                    else
                    {
                        slotBar.Add(maJiang);

                        UpdateCamera(GetMajiangLeftCount());
                    }
                }
                else if (hit.collider.gameObject.TryGetComponent<Slot>(out var slot))
                {
                    if (slot.IsLock)
                    {
                        var lockSlot = slotBar.GetFirstLockSlot();
                        if (lockSlot == null)
                            return;
                        battleHandler?.Pause();
                        Platform.Instance.ShowVideoAd(AdType.openSlot, () =>
                        {
                            battleHandler?.Resume();
                            UnlockSlot();
                        });
                    }
                }
            }
        }
    }

    public void UnlockSlot()
    {
        slotBar.UnLockSlot();
    }

    private void UpdateCamera(int leftMajiangCount)
    {
        var progress = (leftMajiangCount - 60) / (200f - 60);
        progress = Mathf.Max(progress, 0);
        initCameraPos.z = Mathf.Lerp(-0.4f, -0.2f, progress);
        mainCamera.DOFieldOfView(Mathf.Lerp(66, 72, progress), 0.2f);
        mainCamera.transform.DOMove(initCameraPos, 0.2f);
        // mainCamera.fieldOfView = Mathf.Lerp(66, 72, progress);
        // mainCamera.transform.position = initCameraPos;

        // MoveBar(Mathf.Lerp(66, 72, progress));
    }

    private void OnDestroy()
    {
        if (Camera.main != null)
        {
            var cameraStack = Camera.main.GetUniversalAdditionalCameraData().cameraStack;
            if (cameraStack.Contains(mainCamera))
            {
                cameraStack.Remove(mainCamera);
            }
        }
    }

    internal void Test(int[] nums, float delay)
    {
        // for (int i = 0; i < nums.Length; i++)
        // {
        //     var majiangs = FindMajiang(nums[i], 1);
        //     if (majiangs == null)
        //         continue;
        //     for (int j = 0; j < majiangs.Length; j++)
        //     {
        //         var index = j;
        //         if (i != 0)
        //         {
        //             DOVirtual.DelayedCall(delay, () =>
        //             {
        //                 if (this == null) return;
        //                 slotBar.Add(majiangs[index]);
        //             });
        //         }
        //         else
        //         {
        //             slotBar.Add(majiangs[index]);
        //         }

        //     }
        // }
    }

    internal bool MajinagIsClear
    {
        get
        {
            return GetMajiangLeftCount() == 0;
        }
    }

    internal int GetMajiangTotalCount()
    {
        return totalCount;
    }
    internal int GetMajiangLeftCount()
    {
        return MajiangManager.Inst.GetMajiangLeftCount();
    }


    internal bool UseBulbItem()
    {
        var validMajiangVos = slotBar.GetValidMatchMajiangVos();
        var emptyCount = slotBar.GetEmptySlotCount();

        // 遍历槽位中满足条件的麻将，尝试找到可以配对的
        foreach (var majiangVo in validMajiangVos)
        {
            var matchingMajiang = FindMatchingMajiangWithSpaceCheck(majiangVo, emptyCount);
            if (matchingMajiang != null)
            {
                slotBar.Add(matchingMajiang);
                return true;
            }
        }

        // 如果槽位中没有满足条件的麻将，从场上随机选择
        if (validMajiangVos.Count == 0)
        {
            var majiangGroupDic = MajiangManager.Inst.GetMajiangDic();
            foreach (var item in majiangGroupDic)
            {
                var firstMajiang = item.Value;
                // 计算添加两张牌所需的空间，并检查配对牌是否满足剩余空间
                var remainingSpace = emptyCount - firstMajiang.Space;
                if (remainingSpace > 0)
                {
                    var matchingMajiang = FindMatchingMajiangWithSpaceCheck(firstMajiang.MajiangVo, remainingSpace);
                    if (matchingMajiang != null)
                    {
                        slotBar.Add(firstMajiang);
                        slotBar.Add(matchingMajiang);
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private MaJiangGroup FindMatchingMajiangWithSpaceCheck(MaJiangVo targetVo, int availableSpace)
    {
        var majiangGroupDic = MajiangManager.Inst.GetMajiangDic();
        foreach (var item in majiangGroupDic)
        {
            var majiang = item.Value;
            if (majiang.IsItem) continue;
            if (majiang.Id == targetVo.id) continue; // 跳过自己

            // 检查是否能匹配：相同类型和num1
            if (majiang.Type == targetVo.type && majiang.MajiangVo.num1 == targetVo.num1)
            {
                var targetIsDouble = targetVo.num2 != 0;
                var candidateIsDouble = majiang.MajiangVo.num2 != 0;

                // 匹配规则：单牌和双牌可以匹配，双牌和双牌可以匹配，单牌和单牌不能匹配
                if (targetIsDouble || candidateIsDouble)
                {
                    // 检查空间是否足够
                    if (majiang.Space <= availableSpace)
                    {
                        return majiang;
                    }
                    // 如果空间不够，继续寻找其他可能的配对牌
                }
            }
        }
        return null;
    }

    public void UseTurnItem()
    {
        EffectPool.Play("EffectFlip", 3);
        var majiangGroupDic = MajiangManager.Inst.GetMajiangDic();
        foreach (var item in majiangGroupDic)
        {
            _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
        }
    }

    public void UseShuffleItem()
    {
        isShuffling = true;
        var nums = new List<MaJiangVo>();
        foreach (var item in MajiangManager.Inst.GetMajiangDic())
        {
            nums.Add(item.Value.MajiangVo);
            item.Value.Release();
        }
        MajiangManager.Inst.Clear();
        StartCoroutine(CreateMajiangsInFrames(nums));

        // coverCube.SetActive(true);
        // shuffleCube.SetActive(true);
        // shuffleCube.transform.DOKill();
        // shuffleCube.transform.DORotate(new(0, 360 * 2, 0), 3f, RotateMode.WorldAxisAdd).SetEase(Ease.Linear).OnComplete(() =>
        // {
        //     if (this == null)
        //         return;
        //     UseTurnItem();
        //     shuffleCube.SetActive(false);
        //     DOVirtual.DelayedCall(1f, () =>
        //     {
        //         if (this == null)
        //             return;
        //         coverCube.SetActive(false);
        //     });
        // });
    }

    public bool UseMagnetItem()
    {
        var majiangGroupDic = MajiangManager.Inst.GetMajiangDic();
        var isUsed = false;
        for (int idx = 0; idx < 3; idx++)
        {
            int num = 0;
            foreach (var item in majiangGroupDic)
            {
                if (item.Value.IsItem)
                    continue;

                // num = item.Value.Num;//todo==============================
                if (num == 0)
                    continue;

                MaJiangGroup[] majiangs = MajiangManager.Inst.FindMajiang(num, 3);
                if (majiangs == null)
                    continue;

                isUsed = true;
                for (int i = 0; i < majiangs.Length; i++)
                {
                    var majiang = majiangs[i];
                    if (majiang == null)
                    {
                        Debug.LogWarning("麻将数量不匹配！");
                        continue;
                    }
                    MajiangManager.Inst.RemoveMajiang(majiang.Id);

                    majiang.EnableTouch(false);
                    var startPos = majiang.transform.position;
                    majiang.transform.DOMove(startPos + Vector3.up * 1.1f, 0.2f, false).SetDelay(0.2f).SetEase(Ease.Linear).OnComplete(() =>
                    {
                        if (this == null)
                            return;
                        majiang.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 10, 1).OnComplete(() =>
                        {
                            if (this == null)
                                return;

                            DOVirtual.DelayedCall(0.5f, () =>
                            {
                                if (this == null)
                                    return;
                                majiang.Release();
                            });
                        });
                    });
                }

                DOVirtual.DelayedCall(1.4f, () =>
                {
                    slotBar.CheckWin();
                });
                break;
            }
        }
        return isUsed;
    }

    public void ClearSlotBar()
    {
        slotBar.Clear2Table();
        // DOVirtual.DelayedCall(0.5f, () =>
        // {
        //     if (this == null)
        //         return;
        //     UseShuffleItem();
        // });
    }

    internal void MoveBar(float targetZ)
    {
        for (int i = 0; i < slotBar.slots.Length; i++)
        {
            var slot = slotBar.slots[i];
            var pos = slot.transform.position;
            slot.MoveBar(new Vector3(pos.x, pos.y, targetZ));
        }
    }

    private bool IsTouchUI
    {
        get
        {
            return GRoot.inst.touchTarget != null;
        }
    }
}