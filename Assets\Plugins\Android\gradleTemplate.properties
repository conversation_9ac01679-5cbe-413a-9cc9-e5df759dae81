org.gradle.jvmargs=-Xmx6144M -XX:+UseG1GC
org.gradle.parallel=true
# android.enableR8=**MINIFY_WITH_R_EIGHT**
unityStreamingAssets=**STREAMING_ASSETS**

#com.android.tools.build:gradle:7.2.0
#gradle版本7.2需要jdk11
# org.gradle.java.home=D\:\\Android\\jdk11
org.gradle.java.home=C\:\\Users\\Admin\\.jdks\\ms-17.0.15
#ironSource
android.enableDexingArtifactTransform=false

# Android Resolver Properties Start
android.useAndroidX=true
android.enableJetifier=true
# Android Resolver Properties End
**ADDITIONAL_PROPERTIES**

android.suppressUnsupportedCompileSdk=35
android.aapt2FromMavenOverride=F\:\\AndroidEnv\\AndroidPlayer-bobo-tw\\SDK\\build-tools\\35.0.0\\aapt2.exe
# android.aapt2FromMavenOverride=D\:\\Program Files\\Unity\\Hub\\Editor\\2021.3.7f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\build-tools\\35.0.0\\aapt2.exe