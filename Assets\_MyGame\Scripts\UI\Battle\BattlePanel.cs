using System.Text;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

class BattlePanel : Panel
{
    public BattlePanel()
    {
        packName = "Battle";
        compName = "BattlePanel";
    }
    private GTextInput txtNum;
    private GTextInput txtDelay;
    private MediatorLeftTimeBar leftTimeBar;
    private bool isGameWin;
    private MediatorComboBar comboBar;
    private ItemButton btnBulb;
    private ItemButton btnShuffle;
    private ItemButton btnTurn;
    // private ItemButton btnMagnet;
    private GLoader3D itemEffect;
    private bool isGameDone;
    private GTextField txtTotalCount;
    private GTextField txtLeftCount;
    private StringBuilder sb = new StringBuilder();
    private int canUseItemMax;
    private GComponent tipItem;
    private GObject mask;
    private GButton btnCloseAd;
    private GObject adPlaceholder;
    protected override void DoInitialize()
    {
        isGameWin = false;
        itemEffect = contentPane.GetChild("itemEffect").asLoader3D;
        mask = contentPane.GetChild("mask");
        canUseItemMax = ConfigSetting.Setting.GetItemCanUseMax(GameGlobal.EnterLevel);
        btnBulb = new ItemButton(ItemId.ItemBulb, canUseItemMax, contentPane.GetChild("btnBulb"));
        btnShuffle = new ItemButton(ItemId.ItemShuffle, canUseItemMax, contentPane.GetChild("btnShuffle"));
        btnTurn = new ItemButton(ItemId.ItemTurn, canUseItemMax, contentPane.GetChild("btnTurn"));
        // btnMagnet = new ItemButton(ItemId.ItemMagnet, canUseItemMax, contentPane.GetChild("btnMagnet"));
        adPlaceholder = contentPane.GetChild("adPlaceholder");

        if (GameGlobal.NeedGuide)
        {
            var guideTip = contentPane.GetChild("guideTip").asCom;
            guideTip.visible = true;
        }
        var txtVersion = contentPane.GetChild("txtVersion").asTextField;
        // txtVersion.text = $"ver {GameConfig.GetVer()}";

        var txtLevel = contentPane.GetChild("txtLevel").asTextField;
        txtTotalCount = contentPane.GetChild("txtTotalCount").asTextField;
        txtLeftCount = contentPane.GetChild("txtLeftCount").asTextField;

        txtLevel.text = GameGlobal.EnterLevel.ToString();
        var txtChannel = contentPane.GetChild("txtChannel").asTextField;
        txtChannel.text = GameGlobal.Channel;

        leftTimeBar = AddMediator(new MediatorLeftTimeBar(contentPane.GetChild("leftTimeBar").asCom)) as MediatorLeftTimeBar;
        tipItem = contentPane.GetChild("tipItem").asCom;
        var comboBarCom = contentPane.GetChild("comboBar").asCom;
        var comboTipCom = contentPane.GetChild("comboTip").asCom;
        comboBar = AddMediator(new MediatorComboBar(comboBarCom, comboTipCom)) as MediatorComboBar;
        AddMediator(new MediatorStarBar(contentPane.GetChild("starBar").asCom));

        btnCloseAd = contentPane.GetChild("btnCloseAd").asButton;
        btnCloseAd.visible = GameGlobal.HasBannerAndForcedAd;
        adPlaceholder.visible = GameGlobal.HasBannerAndForcedAd;

        GGISdk.Inst.OnPurchaseSuccess += OnPurchased;

        NotifyMgr.On(NotifyNames.UpdateItemCount, this, OnUpdateItemCount);
        NotifyMgr.On(NotifyNames.AddGameTime, this, OnAddGameTime);
        NotifyMgr.On(NotifyNames.ContinueGame, this, OnContinueGame);
        NotifyMgr.On(NotifyNames.UpdateMajiangCount, this, OnUpdateMajiangCount);
        NotifyMgr.On(NotifyNames.SelectItemMajiang, this, OnSelectItemMajiang);
        NotifyMgr.On(NotifyNames.ResumeGame, this, OnResumeGame);
#if UNITY_EDITOR
        txtNum = contentPane.GetChild("txtNum").asTextInput;
        txtDelay = contentPane.GetChild("txtDelay").asTextInput;
        txtNum.text = StorageMgr.EditorNums;
        txtDelay.text = StorageMgr.EditorDelay;
#endif

        var infoGate = ConfigGate.GetData(GameGlobal.EnterLevel);
        if (infoGate != null && infoGate.isHard)
        {
            Create((HardTipsPanel panel) =>
            {
                panel.SetData(infoGate);
            });
        }

    }

    protected override void OnHide()
    {
        GGISdk.Inst.OnPurchaseSuccess -= OnPurchased;
    }

    private void OnPurchased(string productId)
    {
        switch (productId)
        {
            case ProductIds.NO_ADS:
                GameGlobal.HasBannerAndForcedAd = false;
                btnCloseAd.visible = false;
                adPlaceholder.visible = false;
                GGISdk.Inst.HideBannerAd();
                StorageMgr.Save();
                break;
        }
    }

    private void OnAddGameTime(object data)
    {
        int addTime = (int)data;
        leftTimeBar.AddTime(addTime);
    }

    private void OnUpdateMajiangCount()
    {
        var totalCount = BattleScene.Inst.GetMajiangTotalCount();
        var leftCount = BattleScene.Inst.GetMajiangLeftCount();
        sb.Clear();
        sb.AppendFormat("{0}", totalCount);
        txtTotalCount.text = sb.ToString();

        sb.Clear();
        sb.AppendFormat("{0}", leftCount);
        txtLeftCount.text = sb.ToString();
    }

    private void OnUpdateItemCount()
    {
        UpdateItemCount();
    }
    private void OnContinueGame()
    {
        GameStart(true);
        Resume();
    }
    public void Pause()
    {
        comboBar?.Stop();
        leftTimeBar?.Stop();
    }
    public void Resume()
    {
        comboBar?.Continue();
        leftTimeBar?.Continue();
    }

    public void GameStart(bool failAndContinue = false)
    {
        isGameDone = false;
        InitItem(failAndContinue);

        if (GameGlobal.EnterLevel == 4 && !StorageMgr.AlreadyReview)//弹好评界面
        {
            StorageMgr.AlreadyReview = true;
            Pause();
            Create((GoodReviewPanel panel) =>
            {
                panel.OnClosed = (type) =>
                {
                    Resume();
                };
            });
        }
    }

    public void GameWin()
    {
        GGISdk.Inst.ReportChapterEnd(true);
        isGameDone = true;
        Pause();

        Create<ResultWinPanel>((panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (GameGlobal.BattleType == BattleType.Normal)
                {
                    new CmdEnterBattle().Execute(0, true);
                }
                else
                {
                    //挑战胜利后返回大厅
                    GameRoot.SwitchHandler<LobbyHandler>();
                }

                // var curLevel = GameGlobal.EnterLevel;
                // if (curLevel > 2)
                // {
                //     var preLevel = GameGlobal.EnterLevel - 1;
                //     var infoGate = ConfigGate.GetData(curLevel);
                //     var preInfoGate = ConfigGate.GetData(preLevel);
                //     if (infoGate.majiangTypeCount > preInfoGate.majiangTypeCount)
                //     {
                //         var startNum = preInfoGate.majiangTypeCount + 1;
                //         var typeCount = infoGate.majiangTypeCount - preInfoGate.majiangTypeCount;
                //         Create((CollectPanel collectPanel) =>
                //         {
                //             BattleScene.Inst.ChangeLightRot(true);
                //             collectPanel.SetData(startNum, typeCount);
                //             collectPanel.OnClosed = (type) =>
                //             {
                //                 BattleScene.Inst.ChangeLightRot(false);
                //                 new CmdEnterBattle().Execute(true);
                //             };
                //         });
                //     }
                //     else
                //     {
                //         new CmdEnterBattle().Execute(true);
                //     }
                // }
                // else
                // {
                // new CmdEnterBattle().Execute(0, true);
                // }
            };
        });
    }

    public void GameOver()
    {
        // GGISdk.Inst.ReportChapterEnd(false, leftTimeBar.GetPlayTime());
        isGameDone = true;
        Pause();
        new CmdShowResult().Execute(FailReason.SlotFull, true);
    }

    private void InitItem(bool failAndContinue)
    {
        UpdateItemCount();

        if (failAndContinue)
            return;
        GGISdk.Inst.ResetTempData();

        // var level = GameGlobal.EnterLevel;
        // if (level == 1)
        // {

        // }
        // else if (level == 3)
        // {
        //     GiveFreeItem(level, ItemId.ItemBulb, ConfigSetting.Setting.guideItemCount);
        // }
        // else if (level == 4)
        // {
        //     GiveFreeItem(level, ItemId.ItemShuffle, ConfigSetting.Setting.guideItemCount);
        // }
        // else if (level == 5)
        // {
        //     GiveFreeItem(level, ItemId.ItemTurn, ConfigSetting.Setting.guideItemCount);
        // }
        // else if (level == 6)
        // {
        //     // GiveFreeItem(level, ItemId.ItemMagnet, ConfigSetting.Setting.guideItemCount);
        // }
        // else
        // {
        //     // if (!failAndContinue)
        //     // {
        //     //     int idx = Random.Range(1, 101);
        //     //     int itemId;
        //     //     if (idx <= 40)
        //     //     {
        //     //         itemId = ItemId.ItemBulb;
        //     //     }
        //     //     else if (idx <= 50)
        //     //     {
        //     //         itemId = ItemId.ItemShuffle;
        //     //     }
        //     //     else if (idx <= 70)
        //     //     {
        //     //         itemId = ItemId.ItemTurn;
        //     //     }
        //     //     else
        //     //     {
        //     //         itemId = ItemId.ItemMagnet;
        //     //     }
        //     //     GiveFreeItem(level, itemId, 1);
        //     // }
        // }
    }
    private void GiveFreeItem(int level, int itemId, int count)
    {
        if (GameGlobal.IsFirstTimePlayLevel(level))
        {
            mask.visible = true;
            var freeCount = count;
            GameGlobal.IncrementItemCount(itemId, freeCount);

            ItemButton btnItem = null;
            if (itemId == ItemId.ItemBulb) btnItem = btnBulb;
            else if (itemId == ItemId.ItemShuffle) btnItem = btnShuffle;
            else if (itemId == ItemId.ItemTurn) btnItem = btnTurn;
            // else btnItem = btnMagnet;

            if (btnItem == null)
                return;
            for (int i = 0; i < freeCount; i++)
            {
                var startPos = new Vector2(GRoot.inst.width * 0.5f, GRoot.inst.height * 0.5f);
                if (freeCount > 1)
                {
                    startPos.x += Random.Range(-100, 100);
                    startPos.y += Random.Range(-100, 100);
                }
                UIEffectUtil.FlyItemTo(contentPane, startPos, btnItem.GetPos(), itemId, 1, i * 0.1f + 2f);
            }
            DOVirtual.DelayedCall(2.5f, () =>
            {
                if (contentPane.isDisposed)
                    return;
                UpdateItemCount();
                ShowItemTip(itemId);
                mask.visible = false;
            });
        }
    }

    private void UpdateItemCount()
    {
        btnBulb.SetItemCount(GameGlobal.ItemBulbCount);
        btnShuffle.SetItemCount(GameGlobal.ItemShuffleCount);
        btnTurn.SetItemCount(GameGlobal.ItemTurnCount);
        // btnMagnet.SetItemCount(GameGlobal.ItemMagnetCount);
    }

    private int[] itemIds = new int[] { ItemId.ItemBulb, ItemId.ItemShuffle, ItemId.ItemTurn };
    private void ShowRandomItemTip(float delayShow)
    {
        if (contentPane.isDisposed)
            return;

        DOVirtual.DelayedCall(delayShow, () =>
        {
            var itemId = itemIds[Random.Range(0, itemIds.Length)];
            ShowItemTip(itemId);
            ShowRandomItemTip(Random.Range(30, 60));
        });
    }


    private void ShowItemTip(int itemId)
    {
        var infoItem = ConfigItem.GetData(itemId);
        if (infoItem == null)
            return;
        tipItem.touchable = false;
        var lblItemName = tipItem.GetChild("lblItemName").asTextField;
        var lblItemDesc = tipItem.GetChild("lblItemDesc").asTextField;
        lblItemName.text = infoItem.name;
        lblItemDesc.text = infoItem.desc;
        Vector2 tipPos = Vector2.zero;
        switch (itemId)
        {
            case ItemId.ItemBulb:
                tipPos = btnBulb.GetPos();
                tipPos.x += 20;
                break;
            case ItemId.ItemShuffle:
                tipPos = btnShuffle.GetPos();
                break;
            case ItemId.ItemTurn:
                tipPos = btnTurn.GetPos();
                break;
                // case ItemId.ItemMagnet:
                //     tipPos = btnMagnet.GetPos();
                //     tipPos.x -= 20;
                //     break;
        }
        tipPos.y -= 70;
        tipItem.xy = tipPos;

        tipItem.visible = true;
        DOVirtual.DelayedCall(5, () =>
        {
            if (tipItem.isDisposed)
                return;
            tipItem.visible = false;
        });
    }

    protected override void OnMouseClick(string targetName)
    {
        if (isGameDone)
            return;

        switch (targetName)
        {
            case "btnSetting":
                Create((SettingPanel panel) =>
                {
                    panel.SetData(SettingPanel.SettingViewMode.Pause);
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;

                        if (type == 0)
                        {
                            OnResumeGame();
                        }
                        else if (type == SettingPanel.CloseType_Restart)
                        {
                            GGISdk.Inst.ReportChapterEnd(false);
                        }
                    };
                });
                break;
            case "btnBulb":
                UseItem(ItemId.ItemBulb);
                break;
            case "btnShuffle":
                UseItem(ItemId.ItemShuffle);
                break;
            case "btnTurn":
                UseItem(ItemId.ItemTurn);
                break;
            // case "btnMagnet":
            //     UseItem(ItemId.ItemMagnet);
            //     break;
            case "btnRule":
                Create((GameRulePanel panel) =>
                {
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;
                        Resume();
                    };
                });
                break;
            case "btnShop":
                Create((ShopPanel panel) =>
                {
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;
                        Resume();
                    };
                });
                break;
            case "btnCloseAd":
                Create((RemoveAdPanel panel) =>
                {
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;
                        Resume();
                    };
                });
                break;

#if UNITY_EDITOR
            case "btnCreate":
                StorageMgr.EditorNums = txtNum.text;
                StorageMgr.EditorDelay = txtDelay.text;
                var dealy = string.IsNullOrEmpty(txtDelay.text) ? 0 : float.Parse(txtDelay.text);
                var numAry = txtNum.text.Split(",");
                var nums = new int[numAry.Length];
                for (int i = 0; i < nums.Length; i++)
                {
                    nums[i] = int.Parse(numAry[i]);
                }
                BattleScene.Inst.Test(nums, dealy);
#endif
                break;
        }
    }

    private void OnResumeGame()
    {
        Resume();
    }

    private void UseItem(int itemId, bool isByVideo = false, bool add2UsedCount = true)
    {
        var canUse = false;
        Vector2 flyPos = Vector2.zero;
        switch (itemId)
        {
            case ItemId.ItemBulb:
                if (GameGlobal.ItemBulbCount > 0)
                {
                    if (BattleScene.Inst.UseBulbItem())
                    {
                        GameGlobal.UseBulb(add2UsedCount);
                        GGISdk.Inst.ReportUseItemCount(itemId, isByVideo);
                        canUse = true;
                        SoundManager.PlayEffect("slotRot");
                    }
                    else
                    {
                        TipMgr.ShowTip(LangUtil.GetText("txtNoSlot"));
                        return;
                    }
                }
                flyPos = btnBulb.GetPos();
                break;
            case ItemId.ItemShuffle:
                if (GameGlobal.UseShuffle(add2UsedCount))
                {
                    GGISdk.Inst.ReportUseItemCount(itemId, isByVideo);
                    canUse = true;
                    // SoundManager.PlayEffect("shuffling");
                    SoundManager.PlayEffect("turn");
                    BattleScene.Inst.UseShuffleItem();
                }
                flyPos = btnShuffle.GetPos();
                break;
            case ItemId.ItemTurn:
                if (GameGlobal.UseTurn(add2UsedCount))
                {
                    GGISdk.Inst.ReportUseItemCount(itemId, isByVideo);
                    canUse = true;
                    SoundManager.PlayEffect("turn");
                    BattleScene.Inst.UseTurnItem();
                }
                flyPos = btnTurn.GetPos();
                break;
            case ItemId.ItemMagnet:
                // if (GameGlobal.HasMagnet)
                // {
                //     canUse = true;
                //     var useSuccess = BattleScene.Inst.UseMagnetItem();
                //     if (useSuccess)
                //     {
                //         GGISdk.Inst.ReportUseItemCount(itemId, isByVideo);

                //         itemEffect.url = GetCurPackRes("xitieshi");
                //         itemEffect.visible = true;
                //         Timers.inst.Add(1.5f, 1, (obj) =>
                //         {
                //             if (!itemEffect.isDisposed)
                //             {
                //                 itemEffect.visible = false;
                //             }
                //         });
                //         SoundManager.PlayEffect("magnet3");
                //         GameGlobal.UseMagnet(add2UsedCount);
                //     }
                //     else
                //     {
                //         TipMgr.ShowTip("已经找不到匹配的麻将~");
                //     }
                // }
                // flyPos = btnMagnet.GetPos();
                break;
        }

        if (canUse)
        {
            if (add2UsedCount)
            {
                flyPos.y -= 50;
                UIEffectUtil.FlyItem(contentPane, flyPos, itemId, -1);
            }
            UpdateItemCount();
        }
        else
        {
            ShowBuyItemPanel(itemId);
        }
    }

    private void OnSelectItemMajiang(object data)
    {
        var majiangId = (int)data;
        var majiang = MajiangManager.Inst.GetMajiang(majiangId);
        if (majiang == null)
            return;

        ShowBuyItemPanel(majiang.Num, majiang);
    }

    private void ShowBuyItemPanel(int itemId, MaJiangGroup majiang = null)
    {
        Pause();
        Create((BuyItemPanel panel) =>
        {
            panel.OnBuySuccess = (BuyType type) =>
            {
                // flyPos.y -= 50;
                // UIEffectUtil.FlyItem(contentPane, flyPos, itemId, 1);
                UpdateItemCount();
                if (contentPane.isDisposed)
                    return;

                if (majiang != null)
                {
                    majiang.Release();
                    MajiangManager.Inst.RemoveMajiang(majiang.Id);
                    UseItem(itemId, type == BuyType.Video, false);
                }
                else
                {
                    UseItem(itemId, type == BuyType.Video, true);
                }
            };
            panel.OnClosed = (type) =>
            {
                if (contentPane.isDisposed)
                    return;
                Resume();
            };
            panel.SetData(itemId, majiang == null);
        });
    }

    internal void SetLeftTime(int time)
    {
        leftTimeBar.SetLeftTime(time, () =>
        {
            new CmdShowResult().Execute(FailReason.TimeOut, true);
        });
    }
}